"""
Professional logging system for License Plate Monitoring
"""
import logging
import logging.handlers
import os
from datetime import datetime
import json


class ColoredFormatter(logging.Formatter):
    """Custom formatter with colors and clean formatting"""

    # ANSI color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m',     # Reset
        'BOLD': '\033[1m',      # Bold
        'DIM': '\033[2m'        # Dim
    }

    # Icons for different log levels
    ICONS = {
        'DEBUG': '🔍',
        'INFO': '✅',
        'WARNING': '⚠️',
        'ERROR': '❌',
        'CRITICAL': '🚨'
    }

    def format(self, record):
        # Get color and icon for log level
        color = self.COLORS.get(record.levelname, '')
        reset = self.COLORS['RESET']
        icon = self.ICONS.get(record.levelname, '•')

        # Format timestamp (only show time, not full date)
        timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')

        # Format the message with colors
        if record.levelname in ['ERROR', 'CRITICAL']:
            formatted_msg = f"{color}{self.COLORS['BOLD']}{icon} {record.getMessage()}{reset}"
        elif record.levelname == 'WARNING':
            formatted_msg = f"{color}{icon} {record.getMessage()}{reset}"
        elif record.levelname == 'INFO':
            # Special formatting for different types of info messages
            msg = record.getMessage()
            if 'detected' in msg.lower() or 'plate' in msg.lower():
                formatted_msg = f"{self.COLORS['BOLD']}{color}{icon} {msg}{reset}"
            elif 'successful' in msg.lower() or 'connected' in msg.lower():
                formatted_msg = f"{color}{icon} {msg}{reset}"
            else:
                formatted_msg = f"{self.COLORS['DIM']}{icon} {msg}{reset}"
        else:
            formatted_msg = f"{color}{icon} {record.getMessage()}{reset}"

        # Return formatted log entry
        return f"{self.COLORS['DIM']}[{timestamp}]{reset} {formatted_msg}"


class FileFormatter(logging.Formatter):
    """Clean formatter for file logs (no colors)"""

    def format(self, record):
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
        level = record.levelname.ljust(8)
        return f"[{timestamp}] {level} {record.getMessage()}"


class LicensePlateLogger:
    def __init__(self, config_path="config.json"):
        """Initialize logger with configuration"""
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
    
    def _load_config(self, config_path):
        """Load logging configuration from config file"""
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
                return config.get('logging', {})
        except FileNotFoundError:
            # Default configuration if config file not found
            return {
                "level": "INFO",
                "file": "logs/license_plate_monitor.log",
                "max_file_size_mb": 10,
                "backup_count": 5
            }
    
    def _setup_logger(self):
        """Setup logger with file rotation and console output"""
        logger = logging.getLogger('LicensePlateMonitor')
        logger.setLevel(getattr(logging, self.config.get('level', 'INFO')))
        
        # Clear any existing handlers
        logger.handlers.clear()
        
        # Create logs directory if it doesn't exist
        log_file = self.config.get('file', 'logs/license_plate_monitor.log')
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        # File handler with rotation
        max_bytes = self.config.get('max_file_size_mb', 10) * 1024 * 1024
        backup_count = self.config.get('backup_count', 5)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count
        )
        
        # Console handler with colors
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, self.config.get('level', 'INFO')))

        # Set formatters
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(FileFormatter())
        console_handler.setFormatter(ColoredFormatter())
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def get_logger(self):
        """Get the configured logger instance"""
        return self.logger
    
    def log_capture_event(self, success, image_path=None, error=None):
        """Log image capture events"""
        timestamp = datetime.now().isoformat()
        if success:
            self.logger.info(f"Image captured successfully: {image_path}")
        else:
            self.logger.error(f"Image capture failed: {error}")
    
    def log_api_event(self, success, response=None, error=None):
        """Log API interaction events"""
        timestamp = datetime.now().isoformat()
        if success:
            self.logger.info(f"API call successful: {response}")
        else:
            self.logger.error(f"API call failed: {error}")
    
    def log_license_plate_detected(self, license_plates, image_path):
        """Log detected license plates"""
        if license_plates:
            self.logger.info(f"License plates detected in {image_path}: {license_plates}")
        else:
            self.logger.info(f"No license plates detected in {image_path}")

    def info(self, message):
        """Log info message - compatibility method"""
        self.logger.info(message)

    def error(self, message):
        """Log error message - compatibility method"""
        self.logger.error(message)

    def warning(self, message):
        """Log warning message - compatibility method"""
        self.logger.warning(message)


# Global logger instance
_logger_instance = None

def get_logger():
    """Get global logger instance"""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = LicensePlateLogger()
    return _logger_instance.get_logger()

def get_license_plate_logger():
    """Get the full LicensePlateLogger instance"""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = LicensePlateLogger()
    return _logger_instance
