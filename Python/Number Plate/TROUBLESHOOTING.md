# Troubleshooting Guide

## API Key Issues

### Problem: "API key expired" error even after updating config.json

**Symptoms:**
- You update the API key in `config.json` but still get "API key expired" errors
- The system seems to ignore your config.json changes

**Root Cause:**
The system checks for environment variables that override config.json settings. If you have a `GEMINI_API_KEY` environment variable set, it will override whatever is in config.json.

**Solution:**
1. Check if environment variable is set:
   ```bash
   echo $GEMINI_API_KEY
   ```

2. If it shows an old/expired key, clear it:
   ```bash
   unset GEMINI_API_KEY
   ```

3. Update your config.json with the correct API key

4. To permanently remove from your shell profile:
   - Check your shell: `echo $SHELL`
   - Edit the appropriate file:
     - For bash: `~/.bashrc` or `~/.bash_profile`
     - For zsh: `~/.zshrc`
   - Remove any lines that set `GEMINI_API_KEY`

**Prevention:**
- Always check environment variables when config changes don't take effect
- Use config.json as the primary configuration method
- Only use environment variables for temporary overrides

## RTSP Camera Connection Issues

### Problem: "Connection refused" errors

**Symptoms:**
- Camera ping responds but RTSP connection fails
- Error: "Connection to tcp://IP:PORT failed: Connection refused"

**Common Causes:**
1. **Wrong Port**: Most IP cameras use port 554 for RTSP, not 1935
2. **Camera Configuration**: RTSP service might be disabled
3. **Firewall**: Camera firewall blocking connections
4. **Authentication**: Wrong username/password
5. **Network Issues**: Camera on different subnet

**Diagnostic Steps:**
1. Test network connectivity:
   ```bash
   ping [camera_ip]
   ```

2. Test port accessibility:
   ```bash
   nc -zv [camera_ip] [port]
   ```

3. Try common RTSP ports:
   - 554 (standard RTSP)
   - 8554 (alternative RTSP)
   - 1935 (RTMP, not RTSP)

4. Check camera documentation for correct RTSP URL format

**Solutions:**
- Update config.json with correct port and URL format
- Enable RTSP in camera settings
- Check camera firewall settings
- Verify credentials
- Use mock camera for testing: `--mock` flag

## General Debugging

### Enable Debug Logging
Update config.json:
```json
{
  "logging": {
    "level": "DEBUG"
  }
}
```

### Test Individual Components
1. Test API connection:
   ```bash
   python test_gemini.py
   ```

2. Test with mock camera:
   ```bash
   python main.py --mode object_detection --test --mock
   ```

3. Test real camera:
   ```bash
   python main.py --mode object_detection --test
   ```

### Common Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| "API key expired" | Old API key in environment variable | Clear GEMINI_API_KEY env var |
| "Connection refused" | Wrong port or camera config | Check RTSP port and settings |
| "Import error" | Missing dependencies | Run `pip install -r requirements.txt` |
| "No response text" | API response format changed | Check model name in config |

### Getting Help
1. Check logs in `logs/` directory
2. Run with `--mock` to isolate camera vs AI issues
3. Verify all dependencies are installed
4. Check config.json syntax with JSON validator
