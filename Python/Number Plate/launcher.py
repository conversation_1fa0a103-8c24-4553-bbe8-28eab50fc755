#!/usr/bin/env python3
"""
License Plate Recognition System Launcher
Supports both traditional monitoring and web interface modes
"""

import sys
import argparse
from config_manager import get_config_manager
from logger import get_logger

def main():
    """Main launcher function"""
    parser = argparse.ArgumentParser(
        description="License Plate Recognition System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python launcher.py --web                    # Start web interface (default)
  python launcher.py --web --detection-mode license_plate  # Start web interface in license plate mode
  python launcher.py --web --detection-mode object_detection  # Start web interface in object detection mode
  python launcher.py --monitor                # Start traditional monitoring
  python launcher.py --test                   # Run system test
  python launcher.py --demo                   # Run demo mode
        """
    )
    
    parser.add_argument(
        '--web', 
        action='store_true', 
        help='Start web interface (default mode)'
    )
    
    parser.add_argument(
        '--monitor', 
        action='store_true', 
        help='Start traditional monitoring mode'
    )
    
    parser.add_argument(
        '--test', 
        action='store_true', 
        help='Run system test'
    )
    
    parser.add_argument(
        '--demo', 
        action='store_true', 
        help='Run demo mode'
    )
    
    parser.add_argument(
        '--mock-camera', 
        action='store_true', 
        help='Force use of mock camera'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        default='config.json',
        help='Path to configuration file (default: config.json)'
    )

    parser.add_argument(
        '--detection-mode',
        type=str,
        choices=['license_plate', 'object_detection'],
        help='Set detection mode (license_plate or object_detection)'
    )
    
    args = parser.parse_args()
    
    # If no mode specified, default to web
    if not any([args.web, args.monitor, args.test, args.demo]):
        args.web = True
    
    logger = get_logger()
    config_manager = get_config_manager()

    # Set detection mode if specified
    if args.detection_mode:
        config_manager.set_mode(args.detection_mode)
        logger.info(f"🎯 Detection mode set to: {args.detection_mode}")

    try:
        if args.web:
            logger.info("🌐 Starting License Plate Recognition Web Interface...")
            start_web_interface(args.mock_camera)
            
        elif args.monitor:
            logger.info("🔄 Starting Traditional Monitoring Mode...")
            start_monitoring_mode(args.mock_camera)
            
        elif args.test:
            logger.info("🧪 Running System Test...")
            run_system_test(args.mock_camera)
            
        elif args.demo:
            logger.info("🎬 Running Demo Mode...")
            run_demo_mode()
            
    except KeyboardInterrupt:
        logger.info("\n👋 Shutting down gracefully...")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Fatal error: {str(e)}")
        sys.exit(1)

def start_web_interface(use_mock_camera=False):
    """Start the web interface"""
    try:
        from web_app import run_web_server
        run_web_server()
    except ImportError as e:
        print(f"❌ Failed to import web application: {e}")
        print("💡 Make sure Flask is installed: pip install Flask Flask-CORS")
        sys.exit(1)

def start_monitoring_mode(use_mock_camera=False):
    """Start traditional monitoring mode"""
    try:
        from main import AIVisionMonitor
        
        monitor = AIVisionMonitor(use_mock_camera=use_mock_camera)
        monitor.run()
        
    except ImportError as e:
        print(f"❌ Failed to import monitoring system: {e}")
        sys.exit(1)

def run_system_test(use_mock_camera=False):
    """Run system test"""
    try:
        from main import AIVisionMonitor
        
        print("🧪 Running License Plate Recognition System Test")
        print("=" * 50)
        
        monitor = AIVisionMonitor(use_mock_camera=use_mock_camera)
        
        # Test camera connection
        print("\n📷 Testing camera connection...")
        if monitor.camera_handler.connect():
            print("✅ Camera connection successful")
            
            # Test image capture
            print("📸 Testing image capture...")
            result = monitor.camera_handler.capture_image()
            if result:
                image_path, frame = result
                print(f"✅ Image captured: {image_path}")
                
                # Test AI processing
                print("🤖 Testing AI processing...")
                ai_result = monitor.ai_processor.process_image(image_path)
                if ai_result['success']:
                    print("✅ AI processing successful")
                    print(f"📊 Results: {ai_result}")
                else:
                    print(f"❌ AI processing failed: {ai_result.get('error', 'Unknown error')}")
            else:
                print("❌ Image capture failed")
        else:
            print("❌ Camera connection failed")
        
        monitor.camera_handler.disconnect()
        print("\n✅ System test completed")
        
    except ImportError as e:
        print(f"❌ Failed to import test components: {e}")
        sys.exit(1)

def run_demo_mode():
    """Run demo mode"""
    try:
        import demo
        demo.main()
    except ImportError as e:
        print(f"❌ Failed to import demo: {e}")
        sys.exit(1)

def print_system_info():
    """Print system information"""
    config_manager = get_config_manager()
    config = config_manager.get_config()
    
    print("🚗 License Plate Recognition System")
    print("=" * 40)
    print(f"📋 Current Mode: {config_manager.get_current_mode()}")
    print(f"🎯 System Mode: {config.get('system', {}).get('mode', 'Unknown')}")
    print(f"📁 Images Directory: {config.get('storage', {}).get('images_directory', 'captured_images')}")
    print(f"🤖 AI Provider: {config.get('ai', {}).get('provider', 'Unknown')}")
    print("=" * 40)

if __name__ == '__main__':
    print_system_info()
    main()
