#!/usr/bin/env python3
"""
License Plate Monitoring System - Demo Script
Demonstrates the complete system functionality
"""

import os
import time
from datetime import datetime

from mock_camera import MockCameraHandler
from ai_processor import LicensePlateAIProcessor
from logger import get_logger


def run_demo():
    """Run a complete demonstration of the system"""
    print("🚗 License Plate Monitoring System - DEMO")
    print("=" * 50)
    
    logger = get_logger()
    
    # Phase 1: Camera Demonstration
    print("\n📸 Phase 1: Camera System Demo")
    print("-" * 30)
    
    print("Testing mock camera (generates realistic images with license plates)...")
    
    with MockCameraHandler() as camera:
        if camera.is_connected():
            print("✅ Mock camera connected successfully")
            
            # Capture multiple images to show variety
            captured_images = []
            for i in range(3):
                print(f"📷 Capturing image {i+1}/3...")
                success, image_path = camera.capture_frame()
                if success:
                    captured_images.append(image_path)
                    print(f"   ✅ Saved: {image_path}")
                else:
                    print(f"   ❌ Failed to capture image {i+1}")
                time.sleep(1)
        else:
            print("❌ Camera connection failed")
            return
    
    # Phase 2: AI Processing Demonstration
    print(f"\n🤖 Phase 2: AI Processing Demo")
    print("-" * 30)
    
    ai_processor = LicensePlateAIProcessor()
    
    # Test API connection
    print("Testing Gemini AI connection...")
    if ai_processor.test_api_connection():
        print("✅ AI processor connected successfully")
        
        # Process captured images
        for i, image_path in enumerate(captured_images, 1):
            print(f"\n🔍 Processing image {i}: {os.path.basename(image_path)}")
            
            result = ai_processor.process_image(image_path)
            
            if result['success']:
                plates_detected = result.get('plates_detected', 0)
                print(f"   📊 Analysis complete: {plates_detected} license plate(s) detected")
                
                if plates_detected > 0:
                    for j, plate in enumerate(result['license_plates'], 1):
                        plate_number = plate.get('plate_number', 'Unknown')
                        location = plate.get('location', 'Unknown location')
                        confidence = plate.get('confidence', 'Unknown')
                        print(f"   🏷️  Plate {j}: {plate_number}")
                        print(f"      📍 Location: {location}")
                        print(f"      🎯 Confidence: {confidence}")
                else:
                    print("   ℹ️  No license plates detected in this image")
            else:
                error = result.get('error', 'Unknown error')
                print(f"   ❌ AI processing failed: {error}")
                if "API key" in error:
                    print("   💡 Tip: Set up your Gemini API key to enable AI processing")
                    print("      1. Get API key: https://aistudio.google.com/app/apikey")
                    print("      2. Set: export GEMINI_API_KEY='your_key_here'")
    else:
        print("❌ AI processor connection failed")
        print("💡 This is expected if you haven't set up your Gemini API key yet")
        print("   The camera system works independently of AI processing")
    
    # Phase 3: System Integration Demo
    print(f"\n🔄 Phase 3: Complete System Demo")
    print("-" * 30)
    
    print("The complete system combines:")
    print("✅ Automated image capture every minute")
    print("✅ Real-time license plate detection")
    print("✅ Professional logging and monitoring")
    print("✅ Graceful error handling")
    print("✅ Support for both real and mock cameras")
    
    # Show log files
    print(f"\n📋 Generated Files:")
    print("-" * 20)
    
    # List captured images
    if os.path.exists("captured_images"):
        images = [f for f in os.listdir("captured_images") if f.endswith('.jpg')]
        print(f"📸 Captured Images ({len(images)}):")
        for img in sorted(images)[-5:]:  # Show last 5
            print(f"   • {img}")
    
    # List log files
    if os.path.exists("logs"):
        logs = [f for f in os.listdir("logs") if f.endswith('.log')]
        print(f"📝 Log Files ({len(logs)}):")
        for log in logs:
            print(f"   • logs/{log}")
    
    print(f"\n🎯 Next Steps:")
    print("-" * 15)
    print("1. Set up your Gemini API key for AI processing")
    print("2. Configure your RTSP camera in config.json")
    print("3. Run: python main.py --test (to test with real camera)")
    print("4. Run: python main.py (to start continuous monitoring)")
    
    print(f"\n✨ Demo completed successfully!")
    print("The License Plate Monitoring System is ready for production use.")


if __name__ == "__main__":
    run_demo()
