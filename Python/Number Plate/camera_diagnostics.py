#!/usr/bin/env python3
"""
Camera diagnostics and settings adjustment script
"""
import cv2
import numpy as np
import time
from camera_handler import <PERSON><PERSON><PERSON><PERSON>ra<PERSON><PERSON><PERSON>

def analyze_camera_settings():
    """Analyze current camera settings and image quality"""
    print("🔍 Camera Diagnostics and Settings Analysis")
    print("=" * 60)
    
    try:
        camera = RTSPCameraHandler("config.json")
        
        if camera.connect():
            print("✅ Camera connected successfully!")
            
            # Get camera properties
            cap = camera.cap
            if cap and cap.isOpened():
                print("\n📊 Current Camera Properties:")
                
                # Get basic properties
                width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
                height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
                fps = cap.get(cv2.CAP_PROP_FPS)
                brightness = cap.get(cv2.CAP_PROP_BRIGHTNESS)
                contrast = cap.get(cv2.CAP_PROP_CONTRAST)
                saturation = cap.get(cv2.CAP_PROP_SATURATION)
                exposure = cap.get(cv2.CAP_PROP_EXPOSURE)
                gain = cap.get(cv2.CAP_PROP_GAIN)
                
                print(f"   Resolution: {int(width)}x{int(height)}")
                print(f"   FPS: {fps}")
                print(f"   Brightness: {brightness}")
                print(f"   Contrast: {contrast}")
                print(f"   Saturation: {saturation}")
                print(f"   Exposure: {exposure}")
                print(f"   Gain: {gain}")
                
                # Capture and analyze multiple frames
                print("\n📸 Analyzing image quality...")
                frame_stats = []
                
                for i in range(5):
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        mean_val = np.mean(frame)
                        min_val = np.min(frame)
                        max_val = np.max(frame)
                        std_val = np.std(frame)
                        
                        frame_stats.append({
                            'mean': mean_val,
                            'min': min_val,
                            'max': max_val,
                            'std': std_val
                        })
                        
                        print(f"   Frame {i+1}: Mean={mean_val:.1f}, Min={min_val}, Max={max_val}, Std={std_val:.1f}")
                    
                    time.sleep(0.2)
                
                if frame_stats:
                    avg_mean = np.mean([s['mean'] for s in frame_stats])
                    avg_max = np.mean([s['max'] for s in frame_stats])
                    avg_std = np.mean([s['std'] for s in frame_stats])
                    
                    print(f"\n📈 Average Statistics:")
                    print(f"   Average Mean: {avg_mean:.1f}")
                    print(f"   Average Max: {avg_max:.1f}")
                    print(f"   Average Std Dev: {avg_std:.1f}")
                    
                    # Provide recommendations
                    print(f"\n💡 Recommendations:")
                    if avg_mean < 30:
                        print("   ⚠️  Images are very dark (mean < 30)")
                        print("   🔧 Try increasing camera brightness/exposure")
                        print("   💡 Check if camera is in a well-lit area")
                        print("   💡 Check if lens cap is removed")
                        print("   💡 Check camera's infrared/night mode settings")
                    elif avg_mean < 60:
                        print("   ⚠️  Images are somewhat dark (mean < 60)")
                        print("   🔧 Consider increasing brightness slightly")
                    elif avg_mean > 200:
                        print("   ⚠️  Images may be overexposed (mean > 200)")
                        print("   🔧 Consider reducing brightness/exposure")
                    else:
                        print("   ✅ Image brightness appears reasonable")
                    
                    if avg_std < 10:
                        print("   ⚠️  Low contrast (std dev < 10)")
                        print("   🔧 Try increasing contrast setting")
                    
                    if avg_max < 100:
                        print("   ⚠️  No bright areas detected (max < 100)")
                        print("   💡 Camera may be pointing at a dark surface")
                        print("   💡 Ensure camera has a clear view of the area")
                
            camera.disconnect()
            return True
            
        else:
            print("❌ Failed to connect to camera")
            return False
            
    except Exception as e:
        print(f"❌ Error during diagnostics: {e}")
        return False

def try_adjust_camera_settings():
    """Try to adjust camera settings for better image quality"""
    print("\n🔧 Attempting to Adjust Camera Settings")
    print("=" * 50)
    
    try:
        camera = RTSPCameraHandler("config.json")
        
        if camera.connect():
            cap = camera.cap
            if cap and cap.isOpened():
                print("📝 Attempting to adjust settings...")
                
                # Try to adjust brightness
                original_brightness = cap.get(cv2.CAP_PROP_BRIGHTNESS)
                print(f"   Original brightness: {original_brightness}")
                
                # Try increasing brightness
                success = cap.set(cv2.CAP_PROP_BRIGHTNESS, 0.6)
                if success:
                    new_brightness = cap.get(cv2.CAP_PROP_BRIGHTNESS)
                    print(f"   ✅ Brightness adjusted to: {new_brightness}")
                else:
                    print("   ⚠️  Could not adjust brightness (camera may not support it)")
                
                # Try to adjust exposure
                original_exposure = cap.get(cv2.CAP_PROP_EXPOSURE)
                print(f"   Original exposure: {original_exposure}")
                
                # Try setting auto exposure
                success = cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.75)
                if success:
                    print("   ✅ Auto exposure enabled")
                else:
                    print("   ⚠️  Could not enable auto exposure")
                
                # Try to adjust gain
                original_gain = cap.get(cv2.CAP_PROP_GAIN)
                print(f"   Original gain: {original_gain}")
                
                success = cap.set(cv2.CAP_PROP_GAIN, 50)
                if success:
                    new_gain = cap.get(cv2.CAP_PROP_GAIN)
                    print(f"   ✅ Gain adjusted to: {new_gain}")
                else:
                    print("   ⚠️  Could not adjust gain")
                
                # Capture a test frame with new settings
                print("\n📸 Testing with adjusted settings...")
                time.sleep(1)  # Let camera adjust
                
                ret, frame = cap.read()
                if ret and frame is not None:
                    mean_val = np.mean(frame)
                    print(f"   New frame mean: {mean_val:.1f}")
                    
                    # Save test image
                    timestamp = time.strftime("%Y%m%d_%H%M%S")
                    test_path = f"captured_images/test_adjusted_{timestamp}.jpg"
                    cv2.imwrite(test_path, frame)
                    print(f"   ✅ Test image saved: {test_path}")
                else:
                    print("   ❌ Could not capture test frame")
            
            camera.disconnect()
            return True
            
        else:
            print("❌ Failed to connect to camera")
            return False
            
    except Exception as e:
        print(f"❌ Error adjusting settings: {e}")
        return False

if __name__ == "__main__":
    print("🚗 License Plate Monitoring - Camera Diagnostics")
    print("=" * 60)
    
    # Run diagnostics
    success1 = analyze_camera_settings()
    
    if success1:
        # Try adjusting settings
        success2 = try_adjust_camera_settings()
        
        if success2:
            print("\n🎉 Diagnostics completed! Check the recommendations above.")
        else:
            print("\n⚠️  Diagnostics completed, but could not adjust settings.")
    else:
        print("\n❌ Diagnostics failed. Check camera connection.")
    
    print("\n💡 Additional Tips:")
    print("   • Ensure camera is in a well-lit area")
    print("   • Remove any lens caps or obstructions")
    print("   • Check camera's web interface for manual settings")
    print("   • Consider adjusting camera position for better lighting")
