# API Setup Guide

## Gemini AI API Key Setup

To enable license plate recognition with AI, you need to obtain a Google Gemini API key:

### Step 1: Get a Gemini API Key

1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Sign in with your Google account
3. Click "Get API Key" 
4. Create a new API key or use an existing one
5. Copy the API key

### Step 2: Configure the API Key

1. Open `config.json` in your project directory
2. Replace `YOUR_GEMINI_API_KEY_HERE` with your actual API key:

```json
{
    "gemini": {
        "api_key": "your-actual-api-key-here",
        "model": "gemini-2.5-flash-lite",
        "max_retries": 3
    }
}
```

### Step 3: Test the Configuration

Run the test script to verify your API key works:

```bash
python test_gemini.py
```

## Running Without AI (Camera Only Mode)

If you want to test the camera functionality without AI processing:

1. The system will still capture images from the RTSP camera
2. Images will be saved to the `captured_images/` directory
3. You can manually review captured images
4. AI processing will be skipped with appropriate warnings

## RTSP Camera Configuration

Your camera is configured as:
- URL: `rtsp://admin:admin@*************:1935`
- Username: `admin`
- Password: `admin`
- Timeout: 15 seconds

Make sure your camera is accessible on the network and the credentials are correct.
