"""
Configuration Manager for AI Vision Monitoring System
Handles loading and validation of configuration settings
"""
import json
import os
from typing import Dict, Any, Optional
from logger import get_logger

class ConfigManager:
    def __init__(self, config_path: str = "config.json"):
        """Initialize configuration manager"""
        self.config_path = config_path
        self.logger = get_logger()
        self._config = None
        self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                self._config = json.load(f)
            
            # Override with environment variables if present
            self._apply_env_overrides()
            
            # Validate configuration
            self._validate_config()
            
            self.logger.info(f"✅ Configuration loaded from {self.config_path}")
            return self._config
            
        except FileNotFoundError:
            self.logger.error(f"❌ Config file {self.config_path} not found")
            self._config = self._get_default_config()
            return self._config
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ Invalid JSON in config file: {e}")
            self._config = self._get_default_config()
            return self._config
    
    def _apply_env_overrides(self):
        """Apply environment variable overrides only if config values are missing or placeholders"""
        # API Key override - only if config.json doesn't have a valid key
        config_api_key = self._config.get('ai', {}).get('gemini', {}).get('api_key', '').strip()
        if not config_api_key or config_api_key == "YOUR_GEMINI_API_KEY_HERE":
            if os.getenv('GEMINI_API_KEY'):
                self._config['ai']['gemini']['api_key'] = os.getenv('GEMINI_API_KEY')
                self.logger.info("🔑 Using GEMINI_API_KEY environment variable (config.json key was missing/placeholder)")
        else:
            self.logger.info("🔑 Using API key from config.json (ignoring environment variable)")

        # Mode override
        if os.getenv('AI_MODE'):
            mode = os.getenv('AI_MODE').lower()
            if mode in self._config.get('modes', {}):
                self._config['system']['mode'] = mode

        # RTSP URL override
        if os.getenv('RTSP_URL'):
            self._config['camera']['rtsp']['url'] = os.getenv('RTSP_URL')
    
    def _validate_config(self):
        """Validate configuration settings"""
        # Check if current mode exists
        current_mode = self.get_current_mode()
        if current_mode not in self.get_available_modes():
            self.logger.warning(f"⚠️ Invalid mode '{current_mode}', falling back to 'license_plate'")
            self._config['system']['mode'] = 'license_plate'
        
        # Validate required sections
        required_sections = ['system', 'modes', 'camera', 'capture', 'operation', 'web_server', 'ai', 'logging', 'storage']
        for section in required_sections:
            if section not in self._config:
                self.logger.warning(f"⚠️ Missing config section: {section}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "system": {"mode": "license_plate"},
            "modes": {
                "license_plate": {"name": "License Plate Detection", "enabled": True},
                "object_detection": {"name": "Object Detection", "enabled": True}
            },
            "camera": {
                "rtsp": {"url": "rtsp://localhost:554/stream", "timeout": 15}
            },
            "capture": {
                "interval_seconds": 60,
                "save_images": True,
                "image_quality": 95,
                "auto_reconnect": True
            },
            "operation": {
                "mode": "web",
                "auto_mode": {"interval_seconds": 60, "enabled": False},
                "web_mode": {"enabled": True}
            },
            "web_server": {
                "host": "0.0.0.0",
                "port": 8080,
                "debug": False,
                "auto_open_browser": True
            },
            "ai": {
                "gemini": {"api_key": "", "model": "gemini-2.5-flash-lite"}
            },
            "logging": {"level": "INFO"},
            "storage": {"images_directory": "captured_images"}
        }
    
    def get_config(self) -> Dict[str, Any]:
        """Get the full configuration"""
        return self._config
    
    def get_current_mode(self) -> str:
        """Get the current operating mode"""
        return self._config.get('system', {}).get('mode', 'license_plate')
    
    def get_available_modes(self) -> list:
        """Get list of available modes"""
        return list(self._config.get('modes', {}).keys())
    
    def get_mode_config(self, mode: Optional[str] = None) -> Dict[str, Any]:
        """Get configuration for a specific mode"""
        if mode is None:
            mode = self.get_current_mode()
        return self._config.get('modes', {}).get(mode, {})
    
    def get_camera_config(self) -> Dict[str, Any]:
        """Get camera configuration"""
        return self._config.get('camera', {})
    
    def get_ai_config(self) -> Dict[str, Any]:
        """Get AI configuration"""
        return self._config.get('ai', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration"""
        return self._config.get('logging', {})
    
    def get_storage_config(self) -> Dict[str, Any]:
        """Get storage configuration"""
        return self._config.get('storage', {})

    def get_operation_config(self) -> Dict[str, Any]:
        """Get operation configuration"""
        return self._config.get('operation', {})

    def get_web_server_config(self) -> Dict[str, Any]:
        """Get web server configuration"""
        return self._config.get('web_server', {})

    def get_capture_config(self) -> Dict[str, Any]:
        """Get capture configuration"""
        return self._config.get('capture', {})
    
    def set_mode(self, mode: str) -> bool:
        """Set the current operating mode"""
        if mode in self.get_available_modes():
            self._config['system']['mode'] = mode
            self.logger.info(f"🔄 Mode changed to: {mode}")
            return True
        else:
            self.logger.error(f"❌ Invalid mode: {mode}")
            return False
    
    def save_config(self) -> bool:
        """Save current configuration to file"""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self._config, f, indent=4)
            self.logger.info(f"✅ Configuration saved to {self.config_path}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to save config: {e}")
            return False
    
    def get_rtsp_url(self) -> str:
        """Get the complete RTSP URL with credentials"""
        camera_config = self.get_camera_config()
        rtsp_config = camera_config.get('rtsp', {})
        
        url = rtsp_config.get('url', '')
        username = rtsp_config.get('username', '')
        password = rtsp_config.get('password', '')
        
        # If URL already contains credentials, return as-is
        if '@' in url and '://' in url:
            return url
        
        # Add credentials if provided and URL doesn't have them
        if username and password and '://' in url:
            protocol, rest = url.split('://', 1)
            return f"{protocol}://{username}:{password}@{rest}"
        
        return url
    
    def is_mode_enabled(self, mode: Optional[str] = None) -> bool:
        """Check if a mode is enabled"""
        if mode is None:
            mode = self.get_current_mode()
        mode_config = self.get_mode_config(mode)
        return mode_config.get('enabled', False)
    
    def get_mode_description(self, mode: Optional[str] = None) -> str:
        """Get description for a mode"""
        if mode is None:
            mode = self.get_current_mode()
        mode_config = self.get_mode_config(mode)
        return mode_config.get('description', f'{mode} mode')


# Global config manager instance
config_manager = ConfigManager()

def get_config_manager() -> ConfigManager:
    """Get the global config manager instance"""
    return config_manager
