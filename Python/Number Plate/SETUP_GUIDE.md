# License Plate Monitoring System - Complete Setup Guide

## 🎯 Project Overview

This is a complete **3-Phase License Plate Monitoring System** that:
1. **Connects to RTSP cameras** (or uses mock camera for testing)
2. **Captures images every minute** automatically
3. **Detects license plates using Google Gemini AI**
4. **Logs all detections** with detailed information

## ✅ Current Status: **FULLY IMPLEMENTED**

All 3 phases have been completed and integrated:

### Phase 1: ✅ RTSP Camera Integration
- Real RTSP camera support with authentication
- Mock camera for testing without hardware
- Robust error handling and reconnection logic
- Configurable capture intervals

### Phase 2: ✅ Google Gemini AI Integration  
- License plate detection using Gemini Vision API
- Intelligent image analysis with custom prompts
- Structured result parsing and logging
- Comprehensive error handling

### Phase 3: ✅ Production Features
- Automated scheduling system
- Professional logging with rotation
- Configuration management
- Graceful shutdown handling
- Multiple operation modes

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Get Google Gemini API Key
1. Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. Copy the API key

### 3. Configure API Key
```bash
# Set environment variable
export GEMINI_API_KEY="your_api_key_here"

# OR edit config.json
# Set "api_key": "your_api_key_here" in the gemini section
```

### 4. Test the System

#### Test with Mock Camera (Recommended First)
```bash
# Test single capture with mock camera and AI processing
python main.py --test --mock

# Test AI processor only
python main.py --ai-test
```

#### Test with Real RTSP Camera
```bash
# Update config.json with your RTSP camera URL
# Then test with real camera
python main.py --test
```

### 5. Run Continuous Monitoring

#### With Mock Camera (for testing)
```bash
python main.py --mock
```

#### With Real RTSP Camera
```bash
python main.py
```

## 📁 Project Structure

```
Python/Number Plate/
├── main.py                 # Main application entry point
├── camera_handler.py       # Real RTSP camera interface
├── mock_camera.py         # Mock camera for testing
├── ai_processor.py        # Google Gemini AI integration
├── logger.py              # Logging system
├── config.json            # Configuration file
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
├── logs/                 # Log files directory
├── captured_images/      # Captured images directory
└── README.md            # Project documentation
```

## ⚙️ Configuration

### config.json
```json
{
    "rtsp": {
        "url": "rtsp://admin:admin@192.168.68.63:1935",
        "username": "admin",
        "password": "admin",
        "timeout": 15
    },
    "capture": {
        "interval_minutes": 1,
        "save_images": true,
        "image_quality": 95
    },
    "gemini": {
        "api_key": "",
        "model": "gemini-1.5-flash",
        "max_retries": 3
    },
    "logging": {
        "level": "INFO",
        "file": "logs/license_plate_monitor.log",
        "max_file_size_mb": 10,
        "backup_count": 5
    },
    "storage": {
        "images_directory": "captured_images",
        "keep_images_days": 7
    }
}
```

### Environment Variables (.env)
```bash
# RTSP Camera
RTSP_URL=rtsp://admin:admin@192.168.68.63:1935
RTSP_USERNAME=admin
RTSP_PASSWORD=admin

# Google Gemini API
GEMINI_API_KEY=your_gemini_api_key_here

# Optional overrides
CAPTURE_INTERVAL_MINUTES=1
LOG_LEVEL=INFO
```

## 🎮 Usage Examples

### Command Line Options
```bash
# Test single capture with mock camera
python main.py --test --mock

# Test single capture with real camera  
python main.py --test

# Test AI processor connection
python main.py --ai-test

# Run continuous monitoring with mock camera
python main.py --mock

# Run continuous monitoring with real camera
python main.py

# Get help
python main.py --help
```

### Expected Output
```
License Plate Monitoring System
==================================================
Running single capture and AI processing test...
✓ Image captured: captured_images/mock_capture_20250905_194458.jpg
Processing with AI...
✓ AI processing completed. License plates detected: 1
  Plate 1: ABC123 (Location: front of blue car, Confidence: high)
Test successful!
```

## 📊 Features

### Real-time Monitoring
- Captures images every minute (configurable)
- Automatic license plate detection
- Detailed logging of all events
- Graceful error handling and recovery

### AI-Powered Detection
- Uses Google Gemini Vision API
- Detects multiple license plates per image
- Provides confidence levels and locations
- Handles partial/blurry plates

### Professional Logging
- Separate logs for system events and license plate detections
- Log rotation to prevent disk space issues
- Configurable log levels
- Timestamped entries

### Flexible Camera Support
- Real RTSP cameras with authentication
- Mock camera for testing and development
- Automatic reconnection on failures
- Configurable timeouts and retry logic

## 🔧 Troubleshooting

### Common Issues

1. **"API key expired" Error**
   - Get a new API key from Google AI Studio
   - Update GEMINI_API_KEY environment variable

2. **Camera Connection Failed**
   - Check RTSP URL, username, and password
   - Verify camera is accessible on network
   - Use mock camera for testing: `--mock`

3. **Import Errors**
   - Install dependencies: `pip install -r requirements.txt`
   - Check Python version (3.8+ required)

4. **Permission Errors**
   - Ensure write permissions for logs/ and captured_images/
   - Run with appropriate user permissions

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
python main.py --test --mock
```

## 🎯 Next Steps

The system is fully functional and ready for production use. To deploy:

1. **Set up your Gemini API key**
2. **Configure your RTSP camera details**
3. **Test with real camera**: `python main.py --test`
4. **Start monitoring**: `python main.py`

The system will automatically:
- Capture images every minute
- Detect license plates using AI
- Log all detections with timestamps
- Handle errors gracefully
- Rotate logs to prevent disk issues

## 📞 Support

For issues or questions:
1. Check the logs in `logs/license_plate_monitor.log`
2. Run tests with `--test --mock` to isolate issues
3. Verify API key and camera configuration
4. Check network connectivity for RTSP cameras
