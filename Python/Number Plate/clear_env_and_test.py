#!/usr/bin/env python3
"""
Clear environment variables and test the system
"""

import os
import subprocess
import sys

def main():
    """Clear environment variables and test"""
    print("🧹 Clearing environment variables...")
    
    # Clear the problematic environment variable
    if 'GEMINI_API_KEY' in os.environ:
        del os.environ['GEMINI_API_KEY']
        print("✅ Cleared GEMINI_API_KEY")
    
    if 'GOOGLE_API_KEY' in os.environ:
        del os.environ['GOOGLE_API_KEY']
        print("✅ Cleared GOOGLE_API_KEY")
    
    print("\n🧪 Testing API connection...")
    try:
        result = subprocess.run([sys.executable, "debug_api_key.py"], 
                              capture_output=True, text=True, check=True)
        print("✅ API test successful!")
        
        # Check if the output contains success message
        if "AI Processor connection successful!" in result.stdout:
            print("🎉 System is ready!")
            print("\n🚀 You can now run:")
            print("   python launcher.py --web --detection-mode object_detection")
        else:
            print("⚠️ API test completed but check output for issues")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ API test failed: {e}")
        print("Output:", e.stdout)
        print("Error:", e.stderr)

if __name__ == "__main__":
    main()
