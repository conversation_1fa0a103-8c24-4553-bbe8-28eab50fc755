{"system": {"mode": "license_plate", "description": "AI Vision Monitoring System - Multi-Mode Configuration"}, "modes": {"license_plate": {"name": "License Plate Detection", "description": "Detect and read license plates from camera feed", "ai_prompt_type": "license_plate", "output_format": "detailed", "enabled": true}, "object_detection": {"name": "Object Detection", "description": "General object detection and scene description", "ai_prompt_type": "object_detection", "output_format": "concise", "max_words": 15, "enabled": true}}, "camera": {"type": "rtsp", "rtsp": {"url": "rtsp://admin:admin@*************:1935", "username": "admin", "password": "admin", "timeout": 15, "retry_attempts": 3, "retry_delay": 2}, "mock": {"enabled": true, "fallback_on_failure": true}}, "capture": {"interval_seconds": 60, "save_images": true, "image_quality": 95, "auto_reconnect": true}, "operation": {"mode": "web", "auto_mode": {"interval_seconds": 60, "enabled": false}, "web_mode": {"enabled": true}}, "web_server": {"host": "0.0.0.0", "port": 8080, "debug": false, "auto_open_browser": true}, "ai": {"provider": "gemini", "gemini": {"api_key": "AIzaSyDQncVX6lEDPwQ6JK3X8bRZ-vVCKGtyfuk", "model": "gemini-2.0-flash-001", "max_retries": 3, "timeout": 30}, "enabled": true, "fallback_on_failure": false}, "logging": {"level": "INFO", "file": "logs/ai_vision_monitor.log", "max_file_size_mb": 10, "backup_count": 5, "console_output": true, "colored_output": true}, "storage": {"images_directory": "captured_images", "keep_images_days": 7, "organize_by_date": false, "organize_by_mode": true}, "notifications": {"enabled": false, "webhook_url": "", "email": {"enabled": false, "smtp_server": "", "smtp_port": 587, "username": "", "password": "", "to_addresses": []}}}