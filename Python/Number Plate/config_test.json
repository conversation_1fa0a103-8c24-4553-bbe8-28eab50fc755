{"rtsp": {"url": "0", "username": "", "password": "", "timeout": 10}, "capture": {"interval_minutes": 0.1, "save_images": true, "image_quality": 95}, "gemini": {"api_key": "", "model": "gemini-2.5-flash", "max_retries": 3}, "logging": {"level": "INFO", "file": "logs/license_plate_monitor_test.log", "max_file_size_mb": 10, "backup_count": 5}, "storage": {"images_directory": "captured_images_test", "keep_images_days": 1}}