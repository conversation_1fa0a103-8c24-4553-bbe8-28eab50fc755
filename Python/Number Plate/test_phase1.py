#!/usr/bin/env python3
"""
Test script for Phase 1 of License Plate Monitoring System
Tests basic RTSP camera connection and image capture functionality
"""
import os
import sys
import time
import json
from camera_handler import RTSPCameraHandler
from logger import get_logger, get_license_plate_logger


def test_logger():
    """Test logging functionality"""
    print("Testing logger...")
    logger = get_logger()
    license_plate_logger = get_license_plate_logger()
    
    logger.info("Logger test - INFO level")
    logger.warning("Logger test - WARNING level")
    logger.error("Logger test - ERROR level")
    
    license_plate_logger.log_capture_event(True, "test_image.jpg")
    license_plate_logger.log_capture_event(False, error="Test error")
    
    print("✓ Logger test completed")
    return True


def test_config_loading():
    """Test configuration loading"""
    print("Testing configuration loading...")
    
    # Test with test config
    camera = RTSPCameraHandler("config_test.json")
    
    # Verify config loaded correctly
    assert camera.config['capture']['interval_minutes'] == 0.1
    assert camera.config['storage']['images_directory'] == "captured_images_test"
    
    print("✓ Configuration loading test completed")
    return True


def test_camera_connection():
    """Test camera connection (using local camera or RTSP)"""
    print("Testing camera connection...")
    
    camera = RTSPCameraHandler("config_test.json")
    
    # Test connection
    success = camera.test_connection()
    
    if success:
        print("✓ Camera connection test completed successfully")
        return True
    else:
        print("⚠ Camera connection test failed - this is expected if no camera is available")
        return False


def test_image_capture():
    """Test image capture functionality"""
    print("Testing image capture...")
    
    camera = RTSPCameraHandler("config_test.json")
    
    # Create test images directory
    test_dir = camera.config.get('storage', {}).get('images_directory', 'captured_images_test')
    os.makedirs(test_dir, exist_ok=True)
    
    # Try to capture an image
    if camera.connect():
        result = camera.capture_image()
        camera.disconnect()
        
        if result:
            image_path, frame = result
            print(f"✓ Image capture test completed - saved to {image_path}")
            return True
        else:
            print("⚠ Image capture failed")
            return False
    else:
        print("⚠ Could not connect to camera for capture test")
        return False


def test_alternative_sources():
    """Test with alternative video sources"""
    print("Testing alternative video sources...")
    
    # Test configurations for different sources
    test_configs = [
        {"url": "0", "description": "Default camera (index 0)"},
        {"url": "1", "description": "Secondary camera (index 1)"},
        # Add sample RTSP URLs for testing (commented out)
        # {"url": "rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mp4", "description": "Sample RTSP stream"},
    ]
    
    for config in test_configs:
        print(f"  Testing: {config['description']}")
        
        # Create temporary config
        temp_config = {
            "rtsp": {
                "url": config["url"],
                "username": "",
                "password": "",
                "timeout": 5
            },
            "capture": {"save_images": False},
            "storage": {"images_directory": "temp_test"},
            "logging": {"level": "ERROR"}  # Reduce log noise
        }
        
        # Save temporary config
        with open("temp_test_config.json", "w") as f:
            json.dump(temp_config, f)
        
        try:
            camera = RTSPCameraHandler("temp_test_config.json")
            if camera.connect():
                print(f"    ✓ Successfully connected to {config['description']}")
                camera.disconnect()
                # Clean up temp config
                os.remove("temp_test_config.json")
                return True
            else:
                print(f"    ⚠ Failed to connect to {config['description']}")
        except Exception as e:
            print(f"    ⚠ Error testing {config['description']}: {e}")
        
        # Clean up temp config
        if os.path.exists("temp_test_config.json"):
            os.remove("temp_test_config.json")
    
    print("⚠ No alternative video sources found")
    return False


def main():
    """Run all Phase 1 tests"""
    print("=" * 50)
    print("License Plate Monitoring System - Phase 1 Tests")
    print("=" * 50)
    
    tests = [
        ("Logger", test_logger),
        ("Configuration Loading", test_config_loading),
        ("Camera Connection", test_camera_connection),
        ("Image Capture", test_image_capture),
        ("Alternative Sources", test_alternative_sources),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:.<30} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed >= 2:  # At least logger and config should work
        print("\n✓ Phase 1 basic functionality is working!")
        if passed < total:
            print("⚠ Some tests failed - likely due to missing camera hardware")
            print("  This is normal if you don't have a camera connected")
    else:
        print("\n✗ Phase 1 has critical issues that need to be resolved")
        return 1
    
    print("\nNext steps:")
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. Configure your RTSP camera in config.json")
    print("3. Run: python main.py --test")
    print("4. Start monitoring: python main.py")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
