#!/usr/bin/env python3
"""
Simple test script to debug Gemini API issues
"""
import os
import json
from google import genai
from google.genai import types

def test_gemini():
    """Test basic Gemini API functionality"""
    try:
        # Load API key from config
        with open('config.json', 'r') as f:
            config = json.load(f)

        api_key = config['ai']['gemini']['api_key']
        model_name = config['ai']['gemini']['model']

        print(f"✅ API key found: {api_key[:10]}...")
        print(f"✅ Model: {model_name}")

        # Create client with new SDK
        client = genai.Client(api_key=api_key)
        print("✅ Client created")

        # Test simple generation
        print("🔄 Testing simple text generation...")
        response = client.models.generate_content(
            model=model_name,
            contents="Say hello and confirm the API is working"
        )
        
        print(f"✅ Response received: {type(response)}")
        print(f"Response attributes: {[attr for attr in dir(response) if not attr.startswith('_')]}")
        
        # Try to get text
        try:
            if hasattr(response, 'text'):
                print(f"✅ Response text: {response.text}")
                return True
            else:
                print(f"❌ Could not extract text. Response: {response}")
                return False
        except Exception as e:
            print(f"❌ Error extracting text: {e}")
            print(f"Raw response: {response}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Gemini API...")
    success = test_gemini()
    if success:
        print("🎉 Test successful!")
    else:
        print("💥 Test failed!")
