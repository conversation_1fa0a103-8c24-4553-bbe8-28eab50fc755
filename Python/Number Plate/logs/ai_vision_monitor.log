[2025-09-05 21:32:41] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:32:41] INFO     Gemini AI client initialized successfully
[2025-09-05 21:32:41] INFO     🤖 Testing AI processor connection...
[2025-09-05 21:32:43] ERROR    Gemini API connection test failed: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:32:43] WARNING  AI processor test failed. License plate detection may not work.
[2025-09-05 21:32:43] INFO     📷 Testing camera connection...
[2025-09-05 21:32:43] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 21:32:43] ERROR    RTSP URL not configured
[2025-09-05 21:32:43] WARNING  ❌ Real camera connection failed after all attempts
[2025-09-05 21:32:43] INFO     🎭 Switching to mock camera for demonstration
[2025-09-05 21:32:43] INFO     Connecting to mock camera...
[2025-09-05 21:32:44] INFO     Mock camera connected successfully
[2025-09-05 21:32:44] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 21:32:58] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 21:32:58] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:32:58] INFO     Disconnecting from mock camera
[2025-09-05 21:32:58] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:32:59] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:32:59] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:41:56] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:41:57] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:41:57] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:41:57] INFO     🎭 Using mock camera for testing
[2025-09-05 21:41:57] INFO     Gemini AI client initialized successfully
[2025-09-05 21:41:57] INFO     Connecting to mock camera...
[2025-09-05 21:41:58] INFO     Mock camera connected successfully
[2025-09-05 21:42:23] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:42:23] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:42:23] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:42:23] INFO     🎭 Using mock camera for testing
[2025-09-05 21:42:23] INFO     Gemini AI client initialized successfully
[2025-09-05 21:42:23] INFO     Connecting to mock camera...
[2025-09-05 21:42:24] INFO     Mock camera connected successfully
[2025-09-05 21:42:24] INFO     Generated mock image without license plate
[2025-09-05 21:42:24] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_214224.jpg
[2025-09-05 21:42:24] INFO     Processing image for Object Detection: captured_images/mock_capture_20250905_214224.jpg
[2025-09-05 21:42:26] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Empty road with white dashed lines under a light blue sky.', 'word_count': 11, 'raw_response': 'Empty road with white dashed lines under a light blue sky.', 'image_path': 'captured_images/mock_capture_20250905_214224.jpg'}
[2025-09-05 21:42:33] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:42:34] INFO     🔄 Mode changed to: license_plate
[2025-09-05 21:42:34] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 21:42:34] INFO     🎭 Using mock camera for testing
[2025-09-05 21:42:34] INFO     Gemini AI client initialized successfully
[2025-09-05 21:42:34] INFO     Connecting to mock camera...
[2025-09-05 21:42:35] INFO     Mock camera connected successfully
[2025-09-05 21:42:35] INFO     Generated mock image with license plate: MNO678
[2025-09-05 21:42:35] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_214235.jpg
[2025-09-05 21:42:35] INFO     Processing image for License Plate Detection: captured_images/mock_capture_20250905_214235.jpg
[2025-09-05 21:42:37] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'MNO678', 'location': 'On the rear of a blue rectangular object (simulating a vehicle) on the road.', 'confidence': 'High', 'details': 'The license plate is white text on a dark blue background within a blue rectangular frame.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: MNO678\n- Location: On the rear of a blue rectangular object (simulating a vehicle) on the road.\n- Confidence: High\n- Details: The license plate is white text on a dark blue background within a blue rectangular frame.', 'image_path': 'captured_images/mock_capture_20250905_214235.jpg'}
[2025-09-05 21:42:37] INFO     License plates detected in captured_images/mock_capture_20250905_214235.jpg: [{'plate_number': 'MNO678', 'location': 'On the rear of a blue rectangular object (simulating a vehicle) on the road.', 'confidence': 'High', 'details': 'The license plate is white text on a dark blue background within a blue rectangular frame.'}]
[2025-09-05 21:43:28] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:43:29] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:43:29] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:43:29] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:43:29] INFO     Gemini AI client initialized successfully
[2025-09-05 21:43:29] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@***:***@*************:1935
[2025-09-05 21:43:29] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:43:29] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:43:31] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@***:***@*************:1935
[2025-09-05 21:43:31] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:43:31] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:43:35] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@***:***@*************:1935
[2025-09-05 21:43:35] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:43:35] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:44:09] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:44:09] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:44:09] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:44:09] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:44:09] INFO     Gemini AI client initialized successfully
[2025-09-05 21:44:09] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:44:13] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:44:13] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:44:13] INFO     ✅ Image captured and saved: captured_images/capture_20250905_214413.jpg
[2025-09-05 21:44:13] INFO     Image captured successfully: captured_images/capture_20250905_214413.jpg
[2025-09-05 21:44:13] INFO     Processing image for Object Detection: captured_images/capture_20250905_214413.jpg
[2025-09-05 21:44:14] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person looking down near large illuminated number three sculpture.', 'word_count': 9, 'raw_response': 'Person looking down near large illuminated number three sculpture.', 'image_path': 'captured_images/capture_20250905_214413.jpg'}
[2025-09-05 21:44:28] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:44:29] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:44:29] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:44:29] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:44:29] INFO     Gemini AI client initialized successfully
[2025-09-05 21:44:29] INFO     🤖 Testing AI processor connection...
[2025-09-05 21:44:30] INFO     Gemini API connection test successful. Response: API connection successful...
[2025-09-05 21:44:30] INFO     📷 Testing camera connection...
[2025-09-05 21:44:30] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 21:44:30] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:44:33] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:44:33] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:44:33] INFO     ✅ Real RTSP camera connected successfully!
[2025-09-05 21:44:33] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 21:44:38] INFO     Received signal 15, shutting down gracefully...
[2025-09-05 21:44:38] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:44:38] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 21:44:38] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:44:39] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:44:39] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 21:44:39] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:45:40] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:45:40] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:45:40] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:45:40] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:45:40] INFO     Gemini AI client initialized successfully
[2025-09-05 21:45:40] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:45:43] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:45:43] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:45:43] INFO     ✅ Image captured and saved: captured_images/capture_20250905_214543.jpg
[2025-09-05 21:45:43] INFO     Image captured successfully: captured_images/capture_20250905_214543.jpg
[2025-09-05 21:45:43] INFO     Processing image for Object Detection: captured_images/capture_20250905_214543.jpg
[2025-09-05 21:45:43] ERROR    Error processing image captured_images/capture_20250905_214543.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:45:43] ERROR    API call failed: Error processing image captured_images/capture_20250905_214543.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:47:35] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:47:36] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:47:36] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:47:36] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:47:36] INFO     Gemini AI client initialized successfully
[2025-09-05 21:47:36] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:47:39] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:47:39] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:47:39] INFO     ✅ Image captured and saved: captured_images/capture_20250905_214739.jpg
[2025-09-05 21:47:39] INFO     Image captured successfully: captured_images/capture_20250905_214739.jpg
[2025-09-05 21:47:39] INFO     Processing image for Object Detection: captured_images/capture_20250905_214739.jpg
[2025-09-05 21:47:41] ERROR    Error processing image captured_images/capture_20250905_214739.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:47:41] ERROR    API call failed: Error processing image captured_images/capture_20250905_214739.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:55:23] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:55:24] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:55:24] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:55:24] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:55:25] INFO     Gemini AI client initialized successfully
[2025-09-05 21:55:25] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:55:28] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:55:28] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:55:28] INFO     ✅ Image captured and saved: captured_images/capture_20250905_215528.jpg
[2025-09-05 21:55:28] INFO     Image captured successfully: captured_images/capture_20250905_215528.jpg
[2025-09-05 21:55:28] INFO     Processing image for Object Detection: captured_images/capture_20250905_215528.jpg
[2025-09-05 21:55:28] ERROR    Error processing image captured_images/capture_20250905_215528.jpg: Part.from_text() takes 1 positional argument but 2 were given
[2025-09-05 21:55:28] ERROR    API call failed: Error processing image captured_images/capture_20250905_215528.jpg: Part.from_text() takes 1 positional argument but 2 were given
[2025-09-05 21:55:47] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:55:48] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:55:48] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:55:48] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:55:48] INFO     Gemini AI client initialized successfully
[2025-09-05 21:55:48] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:55:48] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:55:48] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:55:50] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:55:50] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:55:50] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:55:54] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:55:55] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:55:55] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:56:02] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:56:03] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:56:03] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:56:03] INFO     🎭 Using mock camera for testing
[2025-09-05 21:56:03] INFO     Gemini AI client initialized successfully
[2025-09-05 21:56:03] INFO     Connecting to mock camera...
[2025-09-05 21:56:04] INFO     Mock camera connected successfully
[2025-09-05 21:56:04] INFO     Generated mock image with license plate: YZA890
[2025-09-05 21:56:04] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_215604.jpg
[2025-09-05 21:56:04] INFO     Processing image for Object Detection: captured_images/mock_capture_20250905_215604.jpg
[2025-09-05 21:56:06] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Blue vehicle with license plate YZA890 on gray road with dashed white lines.', 'word_count': 13, 'raw_response': 'Blue vehicle with license plate YZA890 on gray road with dashed white lines.', 'image_path': 'captured_images/mock_capture_20250905_215604.jpg'}
[2025-09-05 21:56:13] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:56:14] INFO     🔄 Mode changed to: license_plate
[2025-09-05 21:56:14] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 21:56:14] INFO     🎭 Using mock camera for testing
[2025-09-05 21:56:14] INFO     Gemini AI client initialized successfully
[2025-09-05 21:56:14] INFO     Connecting to mock camera...
[2025-09-05 21:56:15] INFO     Mock camera connected successfully
[2025-09-05 21:56:15] INFO     Generated mock image with license plate: VWX567
[2025-09-05 21:56:15] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_215615.jpg
[2025-09-05 21:56:15] INFO     Processing image for License Plate Detection: captured_images/mock_capture_20250905_215615.jpg
[2025-09-05 21:56:17] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'VWX567', 'location': 'Appears to be on the front of a blue vehicle', 'confidence': 'High', 'details': 'The license plate is in blue and white with the text "VWX567" visible.'}], 'plates_detected': 1, 'raw_response': '```\nLICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: VWX567\n- Location: Appears to be on the front of a blue vehicle\n- Confidence: High\n- Details: The license plate is in blue and white with the text "VWX567" visible.\n```', 'image_path': 'captured_images/mock_capture_20250905_215615.jpg'}
[2025-09-05 21:56:17] INFO     License plates detected in captured_images/mock_capture_20250905_215615.jpg: [{'plate_number': 'VWX567', 'location': 'Appears to be on the front of a blue vehicle', 'confidence': 'High', 'details': 'The license plate is in blue and white with the text "VWX567" visible.'}]
[2025-09-05 21:57:43] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:57:45] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:57:45] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:57:45] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:57:45] INFO     Gemini AI client initialized successfully
[2025-09-05 21:57:45] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:45] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:57:45] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:57:47] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:48] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:57:48] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:57:52] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:52] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:57:52] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:57:55] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:57:56] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:57:56] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:57:56] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:57:56] INFO     Gemini AI client initialized successfully
[2025-09-05 21:57:56] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:56] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:57:56] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:57:58] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:58] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:57:58] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:58:02] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:58:03] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:58:03] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:58:19] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:58:20] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:58:20] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:58:20] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:58:20] INFO     Gemini AI client initialized successfully
[2025-09-05 21:58:20] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:58:20] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:58:20] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:58:22] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:58:22] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:58:22] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:58:26] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:58:26] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:58:26] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:59:48] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:59:50] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:59:50] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:59:50] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:59:50] INFO     Gemini AI client initialized successfully
[2025-09-05 21:59:50] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:59:52] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:59:52] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:59:52] INFO     ✅ Image captured and saved: captured_images/capture_20250905_215952.jpg
[2025-09-05 21:59:52] INFO     Image captured successfully: captured_images/capture_20250905_215952.jpg
[2025-09-05 21:59:52] INFO     Processing image for Object Detection: captured_images/capture_20250905_215952.jpg
[2025-09-05 21:59:52] ERROR    Error processing image captured_images/capture_20250905_215952.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 21:59:52] ERROR    API call failed: Error processing image captured_images/capture_20250905_215952.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:01:44] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:01:46] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:01:46] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:01:46] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:01:46] INFO     Gemini AI client initialized successfully
[2025-09-05 22:01:46] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:01:48] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:01:48] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:01:48] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220148.jpg
[2025-09-05 22:01:48] INFO     Image captured successfully: captured_images/capture_20250905_220148.jpg
[2025-09-05 22:01:48] INFO     Processing image for Object Detection: captured_images/capture_20250905_220148.jpg
[2025-09-05 22:01:50] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'A white lamp illuminates the corner with rounded architectural features, all in bright, minimalistic style.', 'word_count': 15, 'raw_response': 'A white lamp illuminates the corner with rounded architectural features, all in bright, minimalistic style.\n', 'image_path': 'captured_images/capture_20250905_220148.jpg'}
[2025-09-05 22:01:56] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:01:57] INFO     🔄 Mode changed to: license_plate
[2025-09-05 22:01:57] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:01:57] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:01:57] INFO     Gemini AI client initialized successfully
[2025-09-05 22:01:57] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:02:00] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:02:00] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:02:00] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220200.jpg
[2025-09-05 22:02:00] INFO     Image captured successfully: captured_images/capture_20250905_220200.jpg
[2025-09-05 22:02:00] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_220200.jpg
[2025-09-05 22:02:02] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.', 'image_path': 'captured_images/capture_20250905_220200.jpg'}
[2025-09-05 22:02:02] INFO     No license plates detected in captured_images/capture_20250905_220200.jpg
[2025-09-05 22:04:01] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:04:02] INFO     🔄 Mode changed to: license_plate
[2025-09-05 22:04:02] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:04:02] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:04:02] INFO     Gemini AI client initialized successfully
[2025-09-05 22:04:02] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:04:04] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:04:04] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:04:04] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220404.jpg
[2025-09-05 22:04:04] INFO     Image captured successfully: captured_images/capture_20250905_220404.jpg
[2025-09-05 22:04:04] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_220404.jpg
[2025-09-05 22:04:06] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.\n', 'image_path': 'captured_images/capture_20250905_220404.jpg'}
[2025-09-05 22:04:06] INFO     No license plates detected in captured_images/capture_20250905_220404.jpg
[2025-09-05 22:04:39] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:04:40] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:04:40] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:04:40] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:04:40] INFO     Gemini AI client initialized successfully
[2025-09-05 22:04:40] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:04:42] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:04:42] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:04:42] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220442.jpg
[2025-09-05 22:04:42] INFO     Image captured successfully: captured_images/capture_20250905_220442.jpg
[2025-09-05 22:04:42] INFO     Processing image for Object Detection: captured_images/capture_20250905_220442.jpg
[2025-09-05 22:04:44] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': "Person's face is visible, possibly looking down, in a brightly lit indoor space.", 'word_count': 13, 'raw_response': "Person's face is visible, possibly looking down, in a brightly lit indoor space.\n", 'image_path': 'captured_images/capture_20250905_220442.jpg'}
[2025-09-05 22:06:02] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:06:03] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:06:03] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:06:03] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:06:03] INFO     Gemini AI client initialized successfully
[2025-09-05 22:06:03] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:06:06] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:06:06] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:06:06] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220606.jpg
[2025-09-05 22:06:06] INFO     Image captured successfully: captured_images/capture_20250905_220606.jpg
[2025-09-05 22:06:06] INFO     Processing image for Object Detection: captured_images/capture_20250905_220606.jpg
[2025-09-05 22:06:08] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Interior scene shows tile floor, kitchen area, and decorative light fixtures.', 'word_count': 11, 'raw_response': 'Interior scene shows tile floor, kitchen area, and decorative light fixtures.\n', 'image_path': 'captured_images/capture_20250905_220606.jpg'}
[2025-09-05 22:06:20] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:06:21] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:06:21] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:06:21] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:06:21] INFO     Gemini AI client initialized successfully
[2025-09-05 22:06:21] INFO     🤖 Testing AI processor connection...
[2025-09-05 22:06:22] INFO     Gemini API connection test successful. Response: API connection successful
...
[2025-09-05 22:06:22] INFO     📷 Testing camera connection...
[2025-09-05 22:06:22] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 22:06:22] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:06:25] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:06:25] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:06:25] INFO     ✅ Real RTSP camera connected successfully!
[2025-09-05 22:06:25] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 22:07:25] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220725.jpg
[2025-09-05 22:07:25] INFO     Image captured successfully: captured_images/capture_20250905_220725.jpg
[2025-09-05 22:07:25] INFO     📷 Image captured: capture_20250905_220725.jpg
[2025-09-05 22:07:25] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:07:25] INFO     Processing image for Object Detection: captured_images/capture_20250905_220725.jpg
[2025-09-05 22:07:27] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Overturned plant and broken mirror inside, city buildings visible outside window.', 'word_count': 11, 'raw_response': 'Overturned plant and broken mirror inside, city buildings visible outside window.\n', 'image_path': 'captured_images/capture_20250905_220725.jpg'}
[2025-09-05 22:07:27] INFO     👁️  Scene: Overturned plant and broken mirror inside, city buildings visible outside window. (11 words)
[2025-09-05 22:07:27] INFO     SCENE: Overturned plant and broken mirror inside, city buildings visible outside window. | 11 words | capture_20250905_220725.jpg
[2025-09-05 22:08:27] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220827.jpg
[2025-09-05 22:08:27] INFO     Image captured successfully: captured_images/capture_20250905_220827.jpg
[2025-09-05 22:08:27] INFO     📷 Image captured: capture_20250905_220827.jpg
[2025-09-05 22:08:27] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:08:27] INFO     Processing image for Object Detection: captured_images/capture_20250905_220827.jpg
[2025-09-05 22:08:30] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'View from window: City buildings at night, indoor plant and objects visible.', 'word_count': 12, 'raw_response': 'View from window: City buildings at night, indoor plant and objects visible.\n', 'image_path': 'captured_images/capture_20250905_220827.jpg'}
[2025-09-05 22:08:30] INFO     👁️  Scene: View from window: City buildings at night, indoor plant and objects visible. (12 words)
[2025-09-05 22:08:30] INFO     SCENE: View from window: City buildings at night, indoor plant and objects visible. | 12 words | capture_20250905_220827.jpg
[2025-09-05 22:09:30] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220930.jpg
[2025-09-05 22:09:30] INFO     Image captured successfully: captured_images/capture_20250905_220930.jpg
[2025-09-05 22:09:30] INFO     📷 Image captured: capture_20250905_220930.jpg
[2025-09-05 22:09:30] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:09:30] INFO     Processing image for Object Detection: captured_images/capture_20250905_220930.jpg
[2025-09-05 22:09:32] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Potted plant sits inside, overlooking city buildings at night through window.', 'word_count': 11, 'raw_response': 'Potted plant sits inside, overlooking city buildings at night through window.\n', 'image_path': 'captured_images/capture_20250905_220930.jpg'}
[2025-09-05 22:09:32] INFO     👁️  Scene: Potted plant sits inside, overlooking city buildings at night through window. (11 words)
[2025-09-05 22:09:32] INFO     SCENE: Potted plant sits inside, overlooking city buildings at night through window. | 11 words | capture_20250905_220930.jpg
[2025-09-05 22:10:33] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221033.jpg
[2025-09-05 22:10:33] INFO     Image captured successfully: captured_images/capture_20250905_221033.jpg
[2025-09-05 22:10:33] INFO     📷 Image captured: capture_20250905_221033.jpg
[2025-09-05 22:10:33] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:10:33] INFO     Processing image for Object Detection: captured_images/capture_20250905_221033.jpg
[2025-09-05 22:10:35] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Indoor scene with broken plant pot, looking out at city buildings at night.', 'word_count': 13, 'raw_response': 'Indoor scene with broken plant pot, looking out at city buildings at night.\n', 'image_path': 'captured_images/capture_20250905_221033.jpg'}
[2025-09-05 22:10:35] INFO     👁️  Scene: Indoor scene with broken plant pot, looking out at city buildings at night. (13 words)
[2025-09-05 22:10:35] INFO     SCENE: Indoor scene with broken plant pot, looking out at city buildings at night. | 13 words | capture_20250905_221033.jpg
[2025-09-05 22:11:06] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:11:08] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:11:08] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:11:08] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:11:08] INFO     Gemini AI client initialized successfully
[2025-09-05 22:11:08] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:11:10] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:11:10] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:11:10] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221110.jpg
[2025-09-05 22:11:10] INFO     Image captured successfully: captured_images/capture_20250905_221110.jpg
[2025-09-05 22:11:10] INFO     Processing image for Object Detection: captured_images/capture_20250905_221110.jpg
[2025-09-05 22:11:13] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Leather chair next to a window with wood floors and a wall air conditioner.', 'word_count': 14, 'raw_response': 'Leather chair next to a window with wood floors and a wall air conditioner.\n', 'image_path': 'captured_images/capture_20250905_221110.jpg'}
[2025-09-05 22:11:35] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221135.jpg
[2025-09-05 22:11:35] INFO     Image captured successfully: captured_images/capture_20250905_221135.jpg
[2025-09-05 22:11:35] INFO     📷 Image captured: capture_20250905_221135.jpg
[2025-09-05 22:11:35] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:11:35] INFO     Processing image for Object Detection: captured_images/capture_20250905_221135.jpg
[2025-09-05 22:11:38] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Fallen pot with plant inside a room, city view outside window.', 'word_count': 11, 'raw_response': 'Fallen pot with plant inside a room, city view outside window.\n', 'image_path': 'captured_images/capture_20250905_221135.jpg'}
[2025-09-05 22:11:38] INFO     👁️  Scene: Fallen pot with plant inside a room, city view outside window. (11 words)
[2025-09-05 22:11:38] INFO     SCENE: Fallen pot with plant inside a room, city view outside window. | 11 words | capture_20250905_221135.jpg
[2025-09-05 22:12:38] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221238.jpg
[2025-09-05 22:12:38] INFO     Image captured successfully: captured_images/capture_20250905_221238.jpg
[2025-09-05 22:12:38] INFO     📷 Image captured: capture_20250905_221238.jpg
[2025-09-05 22:12:38] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:12:38] INFO     Processing image for Object Detection: captured_images/capture_20250905_221238.jpg
[2025-09-05 22:12:41] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Overturned pot with plants, seen through window with city lights at night.', 'word_count': 12, 'raw_response': 'Overturned pot with plants, seen through window with city lights at night.\n', 'image_path': 'captured_images/capture_20250905_221238.jpg'}
[2025-09-05 22:12:41] INFO     👁️  Scene: Overturned pot with plants, seen through window with city lights at night. (12 words)
[2025-09-05 22:12:41] INFO     SCENE: Overturned pot with plants, seen through window with city lights at night. | 12 words | capture_20250905_221238.jpg
[2025-09-05 22:13:41] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221341.jpg
[2025-09-05 22:13:41] INFO     Image captured successfully: captured_images/capture_20250905_221341.jpg
[2025-09-05 22:13:41] INFO     📷 Image captured: capture_20250905_221341.jpg
[2025-09-05 22:13:41] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:13:41] INFO     Processing image for Object Detection: captured_images/capture_20250905_221341.jpg
[2025-09-05 22:13:43] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Potted plant fallen over inside, with a night cityscape seen through the window.', 'word_count': 13, 'raw_response': 'Potted plant fallen over inside, with a night cityscape seen through the window.\n', 'image_path': 'captured_images/capture_20250905_221341.jpg'}
[2025-09-05 22:13:43] INFO     👁️  Scene: Potted plant fallen over inside, with a night cityscape seen through the window. (13 words)
[2025-09-05 22:13:43] INFO     SCENE: Potted plant fallen over inside, with a night cityscape seen through the window. | 13 words | capture_20250905_221341.jpg
[2025-09-05 22:14:44] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221444.jpg
[2025-09-05 22:14:44] INFO     Image captured successfully: captured_images/capture_20250905_221444.jpg
[2025-09-05 22:14:44] INFO     📷 Image captured: capture_20250905_221444.jpg
[2025-09-05 22:14:44] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:14:44] INFO     Processing image for Object Detection: captured_images/capture_20250905_221444.jpg
[2025-09-05 22:14:46] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Potted plant is knocked over indoors with a night cityscape seen through window.', 'word_count': 13, 'raw_response': 'Potted plant is knocked over indoors with a night cityscape seen through window.\n', 'image_path': 'captured_images/capture_20250905_221444.jpg'}
[2025-09-05 22:14:46] INFO     👁️  Scene: Potted plant is knocked over indoors with a night cityscape seen through window. (13 words)
[2025-09-05 22:14:46] INFO     SCENE: Potted plant is knocked over indoors with a night cityscape seen through window. | 13 words | capture_20250905_221444.jpg
[2025-09-05 22:15:46] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221546.jpg
[2025-09-05 22:15:46] INFO     Image captured successfully: captured_images/capture_20250905_221546.jpg
[2025-09-05 22:15:46] INFO     📷 Image captured: capture_20250905_221546.jpg
[2025-09-05 22:15:46] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:15:46] INFO     Processing image for Object Detection: captured_images/capture_20250905_221546.jpg
[2025-09-05 22:15:49] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Fallen flowerpot inside next to window overlooking city at night.', 'word_count': 10, 'raw_response': 'Fallen flowerpot inside next to window overlooking city at night.\n', 'image_path': 'captured_images/capture_20250905_221546.jpg'}
[2025-09-05 22:15:49] INFO     👁️  Scene: Fallen flowerpot inside next to window overlooking city at night. (10 words)
[2025-09-05 22:15:49] INFO     SCENE: Fallen flowerpot inside next to window overlooking city at night. | 10 words | capture_20250905_221546.jpg
[2025-09-05 22:16:49] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221649.jpg
[2025-09-05 22:16:49] INFO     Image captured successfully: captured_images/capture_20250905_221649.jpg
[2025-09-05 22:16:49] INFO     📷 Image captured: capture_20250905_221649.jpg
[2025-09-05 22:16:49] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:16:49] INFO     Processing image for Object Detection: captured_images/capture_20250905_221649.jpg
[2025-09-05 22:16:51] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Fallen potted plant inside with cityscape view outside window.', 'word_count': 9, 'raw_response': 'Fallen potted plant inside with cityscape view outside window.\n', 'image_path': 'captured_images/capture_20250905_221649.jpg'}
[2025-09-05 22:16:51] INFO     👁️  Scene: Fallen potted plant inside with cityscape view outside window. (9 words)
[2025-09-05 22:16:51] INFO     SCENE: Fallen potted plant inside with cityscape view outside window. | 9 words | capture_20250905_221649.jpg
[2025-09-05 22:17:52] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221752.jpg
[2025-09-05 22:17:52] INFO     Image captured successfully: captured_images/capture_20250905_221752.jpg
[2025-09-05 22:17:52] INFO     📷 Image captured: capture_20250905_221752.jpg
[2025-09-05 22:17:52] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:17:52] INFO     Processing image for Object Detection: captured_images/capture_20250905_221752.jpg
[2025-09-05 22:17:54] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Upturned potted plant indoor, with city buildings outside through window, viewed from above.', 'word_count': 13, 'raw_response': 'Upturned potted plant indoor, with city buildings outside through window, viewed from above.\n', 'image_path': 'captured_images/capture_20250905_221752.jpg'}
[2025-09-05 22:17:54] INFO     👁️  Scene: Upturned potted plant indoor, with city buildings outside through window, viewed from above. (13 words)
[2025-09-05 22:17:54] INFO     SCENE: Upturned potted plant indoor, with city buildings outside through window, viewed from above. | 13 words | capture_20250905_221752.jpg
[2025-09-05 22:18:05] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:18:06] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:18:06] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:18:06] INFO     Gemini AI client initialized successfully
[2025-09-05 22:18:06] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:18:19] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:18:54] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221854.jpg
[2025-09-05 22:18:54] INFO     Image captured successfully: captured_images/capture_20250905_221854.jpg
[2025-09-05 22:18:54] INFO     📷 Image captured: capture_20250905_221854.jpg
[2025-09-05 22:18:54] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:18:54] INFO     Processing image for Object Detection: captured_images/capture_20250905_221854.jpg
[2025-09-05 22:18:56] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Potted plant fallen over inside near window, cityscape visible outside.', 'word_count': 10, 'raw_response': 'Potted plant fallen over inside near window, cityscape visible outside.\n', 'image_path': 'captured_images/capture_20250905_221854.jpg'}
[2025-09-05 22:18:56] INFO     👁️  Scene: Potted plant fallen over inside near window, cityscape visible outside. (10 words)
[2025-09-05 22:18:56] INFO     SCENE: Potted plant fallen over inside near window, cityscape visible outside. | 10 words | capture_20250905_221854.jpg
[2025-09-05 22:19:57] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221957.jpg
[2025-09-05 22:19:57] INFO     Image captured successfully: captured_images/capture_20250905_221957.jpg
[2025-09-05 22:19:57] INFO     📷 Image captured: capture_20250905_221957.jpg
[2025-09-05 22:19:57] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:19:57] INFO     Processing image for Object Detection: captured_images/capture_20250905_221957.jpg
[2025-09-05 22:19:59] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Fallen potted plant indoors; outside the window is a city scene at night.', 'word_count': 13, 'raw_response': 'Fallen potted plant indoors; outside the window is a city scene at night.\n', 'image_path': 'captured_images/capture_20250905_221957.jpg'}
[2025-09-05 22:19:59] INFO     👁️  Scene: Fallen potted plant indoors; outside the window is a city scene at night. (13 words)
[2025-09-05 22:19:59] INFO     SCENE: Fallen potted plant indoors; outside the window is a city scene at night. | 13 words | capture_20250905_221957.jpg
[2025-09-05 22:20:21] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:20:21] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:20:23] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:20:23] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:20:23] INFO     Gemini AI client initialized successfully
[2025-09-05 22:20:23] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:20:59] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222059.jpg
[2025-09-05 22:20:59] INFO     Image captured successfully: captured_images/capture_20250905_222059.jpg
[2025-09-05 22:20:59] INFO     📷 Image captured: capture_20250905_222059.jpg
[2025-09-05 22:20:59] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:20:59] INFO     Processing image for Object Detection: captured_images/capture_20250905_222059.jpg
[2025-09-05 22:21:02] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Apartment view with tipped-over plant pot looking out onto city buildings at night.', 'word_count': 13, 'raw_response': 'Apartment view with tipped-over plant pot looking out onto city buildings at night.\n', 'image_path': 'captured_images/capture_20250905_222059.jpg'}
[2025-09-05 22:21:02] INFO     👁️  Scene: Apartment view with tipped-over plant pot looking out onto city buildings at night. (13 words)
[2025-09-05 22:21:02] INFO     SCENE: Apartment view with tipped-over plant pot looking out onto city buildings at night. | 13 words | capture_20250905_222059.jpg
[2025-09-05 22:21:05] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:21:05] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:21:06] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:21:06] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:21:06] INFO     Gemini AI client initialized successfully
[2025-09-05 22:21:06] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:21:23] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:21:24] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:21:25] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:21:25] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:21:25] INFO     Gemini AI client initialized successfully
[2025-09-05 22:21:25] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:22:02] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222202.jpg
[2025-09-05 22:22:02] INFO     Image captured successfully: captured_images/capture_20250905_222202.jpg
[2025-09-05 22:22:02] INFO     📷 Image captured: capture_20250905_222202.jpg
[2025-09-05 22:22:02] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:22:02] INFO     Processing image for Object Detection: captured_images/capture_20250905_222202.jpg
[2025-09-05 22:22:04] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Potted plant lies on floor, cityscape reflection in window at night.', 'word_count': 11, 'raw_response': 'Potted plant lies on floor, cityscape reflection in window at night.\n', 'image_path': 'captured_images/capture_20250905_222202.jpg'}
[2025-09-05 22:22:04] INFO     👁️  Scene: Potted plant lies on floor, cityscape reflection in window at night. (11 words)
[2025-09-05 22:22:04] INFO     SCENE: Potted plant lies on floor, cityscape reflection in window at night. | 11 words | capture_20250905_222202.jpg
[2025-09-05 22:22:57] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:22:57] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:22:57] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:22:57] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:23:01] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:23:01] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:23:01] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:23:01] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:23:02] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:23:02] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:23:02] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:23:02] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:23:02] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:23:02] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:23:02] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:23:02] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:23:03] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:23:03] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:23:03] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:23:03] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:23:03] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:23:03] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:23:03] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:23:03] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:23:03] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:23:03] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:23:03] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:23:03] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:23:04] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222304.jpg
[2025-09-05 22:23:04] INFO     Image captured successfully: captured_images/capture_20250905_222304.jpg
[2025-09-05 22:23:04] INFO     📷 Image captured: capture_20250905_222304.jpg
[2025-09-05 22:23:04] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:23:04] INFO     Processing image for Object Detection: captured_images/capture_20250905_222304.jpg
[2025-09-05 22:23:14] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:23:49] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:23:58] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:24:00] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:24:00] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:24:00] INFO     Gemini AI client initialized successfully
[2025-09-05 22:24:00] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:24:17] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:24:17] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:24:18] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:24:18] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:24:18] INFO     Gemini AI client initialized successfully
[2025-09-05 22:24:18] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:24:28] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:24:28] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:24:28] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:24:28] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 22:24:28] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 22:24:30] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:24:31] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 22:24:31] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 22:24:35] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:24:35] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 22:24:35] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 22:24:35] ERROR    ❌ Failed to reconnect to camera
[2025-09-05 22:24:45] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:24:45] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:24:45] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:24:49] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:24:49] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:24:49] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222449.jpg
[2025-09-05 22:24:49] INFO     Image captured successfully: captured_images/capture_20250905_222449.jpg
[2025-09-05 22:24:49] INFO     📷 Image captured: capture_20250905_222449.jpg
[2025-09-05 22:24:49] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:24:49] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_222449.jpg
[2025-09-05 22:24:49] ERROR    Error processing image captured_images/capture_20250905_222449.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:49] ERROR    API call failed: Error processing image captured_images/capture_20250905_222449.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:49] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_222449.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:53] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:24:53] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222453.jpg
[2025-09-05 22:24:53] INFO     Image captured successfully: captured_images/capture_20250905_222453.jpg
[2025-09-05 22:24:53] INFO     📷 Image captured: capture_20250905_222453.jpg
[2025-09-05 22:24:53] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:24:53] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_222453.jpg
[2025-09-05 22:24:54] ERROR    Error processing image captured_images/capture_20250905_222453.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:54] ERROR    API call failed: Error processing image captured_images/capture_20250905_222453.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:54] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_222453.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:57] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:24:57] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222457.jpg
[2025-09-05 22:24:57] INFO     Image captured successfully: captured_images/capture_20250905_222457.jpg
[2025-09-05 22:24:57] INFO     📷 Image captured: capture_20250905_222457.jpg
[2025-09-05 22:24:57] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:24:57] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_222457.jpg
[2025-09-05 22:24:57] ERROR    Error processing image captured_images/capture_20250905_222457.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:57] ERROR    API call failed: Error processing image captured_images/capture_20250905_222457.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:57] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_222457.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:25:03] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:25:03] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222503.jpg
[2025-09-05 22:25:03] INFO     Image captured successfully: captured_images/capture_20250905_222503.jpg
[2025-09-05 22:25:03] INFO     📷 Image captured: capture_20250905_222503.jpg
[2025-09-05 22:25:03] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:25:03] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_222503.jpg
[2025-09-05 22:25:04] ERROR    Error processing image captured_images/capture_20250905_222503.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:25:04] ERROR    API call failed: Error processing image captured_images/capture_20250905_222503.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:25:04] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_222503.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:25:11] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:25:11] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222511.jpg
[2025-09-05 22:25:11] INFO     Image captured successfully: captured_images/capture_20250905_222511.jpg
[2025-09-05 22:25:11] INFO     📷 Image captured: capture_20250905_222511.jpg
[2025-09-05 22:25:11] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:25:11] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_222511.jpg
[2025-09-05 22:25:11] ERROR    Error processing image captured_images/capture_20250905_222511.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:25:11] ERROR    API call failed: Error processing image captured_images/capture_20250905_222511.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:25:11] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_222511.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:25:24] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:24] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:24] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:24] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:26] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:26] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:26] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:26] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:27] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:27] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:27] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:27] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:27] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:27] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:27] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:27] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:27] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:27] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:27] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:27] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:34] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:25:34] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:25:35] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:25:35] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:25:35] INFO     Gemini AI client initialized successfully
[2025-09-05 22:25:35] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:25:40] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:25:40] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:25:40] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:25:40] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 22:25:40] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 22:25:42] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:25:42] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 22:25:42] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 22:25:46] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:25:46] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 22:25:46] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 22:25:46] ERROR    ❌ Failed to reconnect to camera
[2025-09-05 22:25:56] INFO     🤖 Auto mode worker started (interval: 60s)
[2025-09-05 22:25:56] INFO     🔄 Auto mode started with 60s interval
[2025-09-05 22:25:56] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:25:56] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:25:56] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 22:25:56] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 22:25:58] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:25:58] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 22:25:58] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 22:26:02] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:26:02] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 22:26:02] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 22:26:02] ERROR    ❌ Failed to reconnect to camera
[2025-09-05 22:27:03] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:27:03] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:27:03] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 22:27:03] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 22:27:05] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:27:07] INFO     ✅ Successfully connected to RTSP camera (retry 2, 2 successful reads)
[2025-09-05 22:27:07] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:27:07] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222707.jpg
[2025-09-05 22:27:07] INFO     Image captured successfully: captured_images/capture_20250905_222707.jpg
[2025-09-05 22:27:07] INFO     📷 Image captured: capture_20250905_222707.jpg
[2025-09-05 22:27:07] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:27:07] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_222707.jpg
[2025-09-05 22:27:08] ERROR    Error processing image captured_images/capture_20250905_222707.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:27:08] ERROR    API call failed: Error processing image captured_images/capture_20250905_222707.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:27:08] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_222707.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:27:42] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:27:42] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:27:42] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:27:42] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:27:46] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:27:46] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:27:46] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:27:46] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:27:47] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:27:47] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:27:47] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:27:47] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:36:17] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:36:18] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:36:18] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:36:18] INFO     Gemini AI client initialized successfully
[2025-09-05 22:36:18] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:36:18] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:36:18] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:36:18] INFO     Gemini AI client initialized successfully
[2025-09-05 22:36:18] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:36:25] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:37:53] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:37:53] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:37:53] INFO     🎯 Detection mode set to: object_detection
[2025-09-05 22:37:53] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:37:54] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:37:54] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:37:54] INFO     Gemini AI client initialized successfully
[2025-09-05 22:37:54] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:38:03] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:38:03] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:38:03] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:38:07] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:38:07] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:38:07] INFO     ✅ Image captured and saved: captured_images/capture_20250905_223807.jpg
[2025-09-05 22:38:07] INFO     Image captured successfully: captured_images/capture_20250905_223807.jpg
[2025-09-05 22:38:07] INFO     📷 Image captured: capture_20250905_223807.jpg
[2025-09-05 22:38:07] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:38:07] INFO     Processing image for Object Detection: captured_images/capture_20250905_223807.jpg
[2025-09-05 22:38:07] ERROR    Error processing image captured_images/capture_20250905_223807.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:38:07] ERROR    API call failed: Error processing image captured_images/capture_20250905_223807.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:38:07] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_223807.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:38:07] ERROR    ❌ Capture failed: AI processing failed: Error processing image captured_images/capture_20250905_223807.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:46:54] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:46:54] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:46:54] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:46:54] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:46:55] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:46:55] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:46:55] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:46:55] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:46:55] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:46:55] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:46:55] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:46:55] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:46:55] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:46:55] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:46:55] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:46:55] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:47:02] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:47:02] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:47:02] INFO     🎯 Detection mode set to: object_detection
[2025-09-05 22:47:02] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:47:03] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:47:03] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:47:03] INFO     🔑 Using API key from config.json: AIzaSyAB...dfXg
[2025-09-05 22:47:03] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 22:47:03] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:47:16] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:47:16] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:47:16] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:47:19] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:47:19] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:47:19] INFO     ✅ Image captured and saved: captured_images/capture_20250905_224719.jpg
[2025-09-05 22:47:19] INFO     Image captured successfully: captured_images/capture_20250905_224719.jpg
[2025-09-05 22:47:19] INFO     📷 Image captured: capture_20250905_224719.jpg
[2025-09-05 22:47:19] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:47:19] INFO     Processing image for Object Detection: captured_images/capture_20250905_224719.jpg
[2025-09-05 22:47:19] ERROR    ❌ Error processing image captured_images/capture_20250905_224719.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:47:19] ERROR    API call failed: Error processing image captured_images/capture_20250905_224719.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:47:19] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_224719.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:47:19] ERROR    ❌ Capture failed: AI processing failed: Error processing image captured_images/capture_20250905_224719.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:48:09] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:48:10] INFO     🔑 Using API key from config.json: AIzaSyAB...dfXg
[2025-09-05 22:48:10] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 22:48:10] INFO     🧪 Testing API connection with model: gemini-2.0-flash-001
[2025-09-05 22:48:10] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:48:10] INFO     ✅ Image captured and saved: captured_images/capture_20250905_224810.jpg
[2025-09-05 22:48:10] INFO     Image captured successfully: captured_images/capture_20250905_224810.jpg
[2025-09-05 22:48:10] INFO     📷 Image captured: capture_20250905_224810.jpg
[2025-09-05 22:48:10] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:48:10] INFO     Processing image for Object Detection: captured_images/capture_20250905_224810.jpg
[2025-09-05 22:48:10] ERROR    ❌ Error processing image captured_images/capture_20250905_224810.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:48:10] ERROR    API call failed: Error processing image captured_images/capture_20250905_224810.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:48:10] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_224810.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:48:10] ERROR    ❌ Capture failed: AI processing failed: Error processing image captured_images/capture_20250905_224810.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:48:11] ERROR    ❌ Gemini API connection test failed: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:48:49] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 22:48:49] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:48:50] INFO     🔑 Using API key from config.json: AIzaSyCv...BZTI
[2025-09-05 22:48:50] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 22:48:50] INFO     🧪 Testing API connection with model: gemini-2.0-flash-001
[2025-09-05 22:48:51] INFO     ✅ Gemini API connection test successful. Response: API connection successful
...
[2025-09-05 22:48:53] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:48:53] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:48:53] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:48:53] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:49:06] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 22:49:06] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:49:06] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:49:06] INFO     🎯 Detection mode set to: object_detection
[2025-09-05 22:49:06] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:49:07] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:49:07] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:49:07] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-05 22:49:07] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 22:49:07] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:49:10] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:49:10] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:49:10] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:49:14] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:49:14] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:49:14] INFO     ✅ Image captured and saved: captured_images/capture_20250905_224914.jpg
[2025-09-05 22:49:14] INFO     Image captured successfully: captured_images/capture_20250905_224914.jpg
[2025-09-05 22:49:14] INFO     📷 Image captured: capture_20250905_224914.jpg
[2025-09-05 22:49:14] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:49:14] INFO     Processing image for Object Detection: captured_images/capture_20250905_224914.jpg
[2025-09-05 22:49:16] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'The image is completely black and shows no identifiable objects or features.', 'word_count': 12, 'raw_response': 'The image is completely black and shows no identifiable objects or features.\n', 'image_path': 'captured_images/capture_20250905_224914.jpg'}
[2025-09-05 22:49:16] INFO     👁️  Scene: The image is completely black and shows no identifiable objects or features. (12 words)
[2025-09-05 22:49:16] INFO     SCENE: The image is completely black and shows no identifiable objects or features. | 12 words | capture_20250905_224914.jpg
[2025-09-05 22:49:16] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 22:49:16] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:49:16] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:49:16] INFO     🎯 Detection mode set to: object_detection
[2025-09-05 22:49:16] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:49:17] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:49:17] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:49:17] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-05 22:49:17] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 22:49:17] INFO     ✅ AI Vision Monitor initialized for web interface
