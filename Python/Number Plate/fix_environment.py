#!/usr/bin/env python3
"""
<PERSON>ript to fix environment variable issues and test the system
"""

import os
import subprocess
import sys

def main():
    """Fix environment variables and test system"""
    print("🔧 Fixing Environment Variables")
    print("=" * 40)
    
    # List of environment variables that might interfere
    env_vars_to_check = [
        'GEMINI_API_KEY',
        'GOOGLE_API_KEY',
        'GOOGLE_GENAI_USE_VERTEXAI',
        'GOOGLE_CLOUD_PROJECT',
        'GOOGLE_CLOUD_LOCATION'
    ]
    
    # Check current environment variables
    print("📋 Current Environment Variables:")
    for var in env_vars_to_check:
        value = os.getenv(var)
        if value:
            print(f"  {var}: {'Set' if value else 'Not set'}")
            if var in ['GEMINI_API_KEY', 'GOOGLE_API_KEY'] and value:
                masked = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
                print(f"    Value: {masked}")
        else:
            print(f"  {var}: Not set")
    
    # Ask user if they want to clear environment variables
    print(f"\n🤔 Do you want to clear potentially conflicting environment variables?")
    print("This will ensure config.json takes precedence.")
    response = input("Clear environment variables? (y/n): ").lower().strip()
    
    if response == 'y':
        print("\n🧹 Clearing environment variables...")
        for var in env_vars_to_check:
            if os.getenv(var):
                print(f"  Clearing {var}")
                os.environ.pop(var, None)
        
        print("✅ Environment variables cleared for this session")
        print("💡 To permanently clear them, run:")
        for var in env_vars_to_check:
            print(f"   unset {var}")
    
    # Test the debug script
    print(f"\n🧪 Running API key debug test...")
    try:
        subprocess.run([sys.executable, "debug_api_key.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Debug test failed: {e}")
    except FileNotFoundError:
        print("❌ debug_api_key.py not found")

if __name__ == "__main__":
    main()
