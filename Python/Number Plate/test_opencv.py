#!/usr/bin/env python3
"""
Simple OpenCV test script
"""
try:
    import cv2
    print("✓ OpenCV imported successfully")
    
    # Test version
    try:
        print(f"OpenCV version: {cv2.__version__}")
    except:
        print("⚠ Could not get OpenCV version")
    
    # Test VideoCapture
    try:
        cap = cv2.VideoCapture(0)
        print("✓ VideoCapture created successfully")
        cap.release()
    except Exception as e:
        print(f"✗ VideoCapture failed: {e}")
    
    # Test RTSP URL
    try:
        rtsp_url = "rtsp://admin:admin@*************:1935"
        cap = cv2.VideoCapture(rtsp_url)
        print(f"✓ RTSP VideoCapture created for {rtsp_url}")
        
        if cap.isOpened():
            print("✓ RTSP stream opened successfully")
            ret, frame = cap.read()
            if ret:
                print(f"✓ Frame captured successfully: {frame.shape}")
            else:
                print("⚠ Could not read frame from RTSP stream")
        else:
            print("⚠ Could not open RTSP stream")
        
        cap.release()
    except Exception as e:
        print(f"✗ RTSP test failed: {e}")

except ImportError as e:
    print(f"✗ Failed to import OpenCV: {e}")
except Exception as e:
    print(f"✗ Unexpected error: {e}")
