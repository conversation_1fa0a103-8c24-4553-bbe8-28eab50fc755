# License Plate Monitoring System

A Python-based system that continuously monitors an RTSP camera feed, captures images every minute, and uses Google's Gemini AI to detect license plates.

## 🚀 Features

### Phase 1 (Current) - Basic RTSP Integration
- ✅ RTSP camera connection and monitoring
- ✅ Periodic image capture (configurable interval)
- ✅ Structured logging with file rotation
- ✅ Local image storage with timestamps
- ✅ Graceful shutdown handling

### Phase 2 (Planned) - AI Integration
- 🔄 Google Gemini API integration
- 🔄 License plate detection and recognition
- 🔄 Intelligent image processing
- 🔄 API response parsing and logging

### Phase 3 (Planned) - Production Features
- 🔄 Advanced error handling and retry logic
- 🔄 Configuration management
- 🔄 Performance monitoring
- 🔄 Database storage options

## 📋 Requirements

- Python 3.8+
- RTSP camera or stream
- Google Gemini API key (for Phase 2)

## 🛠️ Installation

1. **Clone or download the project files**

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure the system:**
   
   Copy the example environment file:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your RTSP camera details:
   ```
   RTSP_URL=rtsp://your-camera-ip:554/stream
   RTSP_USERNAME=your_username
   RTSP_PASSWORD=your_password
   ```
   
   Optionally, edit `config.json` for advanced settings.

## 🎯 Usage

### Run Phase 1 Tests
```bash
python test_phase1.py
```

### Test Camera Connection
```bash
python main.py --test
```

### Start Continuous Monitoring
```bash
python main.py
```

### Test Individual Components
```bash
# Test camera handler only
python camera_handler.py

# Test logging
python logger.py
```

## ⚙️ Configuration

### config.json
```json
{
    "rtsp": {
        "url": "rtsp://your-camera-ip:554/stream",
        "username": "",
        "password": "",
        "timeout": 10
    },
    "capture": {
        "interval_minutes": 1,
        "save_images": true,
        "image_quality": 95
    },
    "storage": {
        "images_directory": "captured_images",
        "keep_images_days": 7
    },
    "logging": {
        "level": "INFO",
        "file": "logs/license_plate_monitor.log"
    }
}
```

### Environment Variables (.env)
- `RTSP_URL`: Your RTSP camera stream URL
- `RTSP_USERNAME`: Camera username (if required)
- `RTSP_PASSWORD`: Camera password (if required)
- `CAPTURE_INTERVAL_MINUTES`: Override capture interval
- `GEMINI_API_KEY`: Google Gemini API key (Phase 2)

## 📁 Project Structure

```
license_plate_monitor/
├── main.py                 # Main application entry point
├── camera_handler.py       # RTSP camera connection and capture
├── logger.py              # Logging configuration
├── config.json           # Configuration file
├── .env                  # Environment variables
├── requirements.txt      # Python dependencies
├── logs/                # Log files
├── captured_images/     # Captured images storage
└── README.md           # This file
```

## 🔧 Troubleshooting

### Common Issues

1. **Camera Connection Failed**
   - Verify RTSP URL is correct
   - Check network connectivity to camera
   - Ensure camera supports RTSP
   - Verify username/password if authentication is required

2. **Permission Errors**
   - Ensure write permissions for `logs/` and `captured_images/` directories
   - Run with appropriate user permissions

3. **Import Errors**
   - Install all requirements: `pip install -r requirements.txt`
   - Ensure Python 3.8+ is being used

### Logs
Check the log file for detailed error information:
```bash
tail -f logs/license_plate_monitor.log
```

## 🚦 Phase Development Status

- **Phase 1**: ✅ **COMPLETE** - Basic RTSP camera integration
  - ✅ Project structure and configuration
  - ✅ RTSP camera handler with latest OpenCV best practices
  - ✅ Periodic scheduling with graceful shutdown
  - ✅ Comprehensive logging system
  - ✅ Test suite for validation
- **Phase 2**: 📋 Ready to Start - Google Gemini AI integration
- **Phase 3**: 📋 Planned - Production features and optimization

## 📝 Phase 1 Completion Summary

✅ **Successfully Implemented:**
- Modern project structure with proper separation of concerns
- RTSP camera connection using latest OpenCV 4.10+ best practices
- Robust error handling and timeout management
- Periodic image capture with configurable intervals
- Comprehensive logging with rotation and structured output
- Test suite covering all major components
- Configuration management with environment variable support
- Graceful shutdown handling

✅ **Tested and Validated:**
- Logger functionality with multiple log levels
- Configuration loading and validation
- Camera connection logic (ready for real RTSP streams)
- Image capture and storage workflow
- Alternative video source detection

## 📝 Next Steps for Phase 2

1. **Install dependencies**: `pip install -r requirements.txt`
2. **Configure RTSP camera** in `config.json` or `.env`
3. **Test with real camera**: `python test_phase1.py`
4. **Proceed to Phase 2**: Google Gemini API integration for license plate detection

## 🤝 Support

For issues or questions:
1. Check the logs for error details
2. Verify configuration settings
3. Test individual components separately
